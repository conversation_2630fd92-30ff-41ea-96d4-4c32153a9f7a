import { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { <PERSON><PERSON><PERSON>, Check, Star, Phone, MessageCircle, Clock, Users } from 'lucide-react';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { TrustIndicators } from '@/components/TrustIndicators';
import QuoteRequestForm from '@/components/QuoteRequestForm';
import { services } from '@/data/services';
import { trackBusinessEvents } from '@/lib/analytics';

const Bodenreinigung = () => {
  const serviceData = services.bodenreinigung;

  useEffect(() => {
    trackBusinessEvents.servicePageView('bodenreinigung');
  }, []);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleCTAClick = () => {
    trackBusinessEvents.serviceInquiry('bodenreinigung');
    window.open('https://wa.me/4917623152477', '_blank');
  };

  return (
    <div className="min-h-screen suz-page-container bg-premium-gradient">
      <Helmet>
        <title>{serviceData.seo.title}</title>
        <meta name="description" content={serviceData.seo.description} />
        <meta name="keywords" content={serviceData.seo.keywords.join(', ')} />
        <link rel="canonical" href="https://www.suzreinigung.de/services/bodenreinigung" />
        <meta property="og:title" content={serviceData.seo.title} />
        <meta property="og:description" content={serviceData.seo.description} />
        <meta property="og:url" content="https://www.suzreinigung.de/services/bodenreinigung" />
        <meta property="og:type" content="service" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={serviceData.seo.title} />
        <meta name="twitter:description" content={serviceData.seo.description} />
      </Helmet>

      <Navigation scrollToSection={scrollToSection} />

      {/* Enhanced Hero Section with Logo Integration */}
      <section
        id="home"
        className="relative suz-section-hero suz-hero-enhanced"
        role="banner"
        aria-label="Bodenreinigung Service Hero"
      >
        {/* Hero Logo Integration */}
        <div className="absolute top-6 left-6 z-50 animate-fade-in suz-logo-container suz-hero-logo">
          <div className="suz-card-glass suz-logo-wrapper rounded-3xl border border-white/30 shadow-xl logo-glow group">
            <img
              src="/assets/logos/logo.png"
              alt="SUZ Reinigung Logo - Professionelle Bodenreinigung"
              title="SUZ Reinigung - Premium Bodenreinigung"
              className="suz-logo-enhanced object-contain transition-all duration-300 group-hover:scale-110 image-optimized"
              loading="eager"
              decoding="async"
            />
          </div>
        </div>

        {/* Enhanced Background Effects */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900/95 via-purple-900/90 to-slate-900/95 backdrop-blur-sm"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(147,51,234,0.15),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(59,130,246,0.1),transparent_50%)]"></div>

        <div className="max-w-6xl mx-auto text-center animate-fade-in relative z-10">
          <header className="suz-hero-headline-container mb-8">
            <div
              className="suz-icon-badge-premium mb-8 mx-auto"
              role="img"
              aria-label="Bodenreinigung Service Icon"
              tabIndex={0}
            >
              <Sparkles
                className="w-12 h-12 transition-all duration-500"
                strokeWidth={2}
                aria-hidden="true"
                focusable="false"
              />
            </div>
            <h1
              className="suz-hero-title text-slate-100 optimize-lcp"
              itemProp="name"
              data-ai-content="service-heading"
              role="heading"
              aria-level="1"
            >
              <span
                className="suz-hero-accent gradient-text-animated pulse-glow"
                aria-label={`${serviceData.title} - Premium Reinigungsservice`}
              >
                {serviceData.title}
              </span>
            </h1>
          </header>

          <div className="suz-hero-content-container mb-12">
            <p
              className="suz-text-heading-xl text-slate-300 mb-8 max-w-4xl mx-auto leading-relaxed"
              data-ai-content="service-description"
              itemProp="description"
            >
              {serviceData.longDescription}
            </p>
          </div>

          <div className="suz-hero-cta-container flex flex-col sm:flex-row gap-6 justify-center items-center">
            <button
              type="button"
              onClick={handleCTAClick}
              className="suz-btn-primary suz-btn-cta group"
              aria-label="Kostenloses Angebot für Bodenreinigung anfordern"
            >
              <MessageCircle className="w-5 h-5 transition-transform group-hover:scale-110" />
              <span>Kostenloses Angebot</span>
            </button>
            <a
              href="tel:+4917623152477"
              className="suz-btn-secondary suz-btn-cta group"
              aria-label="SUZ Reinigung anrufen für Bodenreinigung"
            >
              <Phone className="w-5 h-5 transition-transform group-hover:scale-110" />
              <span>Jetzt anrufen</span>
            </a>
          </div>
        </div>
      </section>

      {/* Main Content Wrapper */}
      <main id="main-content" role="main" aria-label="Bodenreinigung Service Inhalt">

        {/* Enhanced Service Image Section */}
        <section
          className="suz-section-standard bg-slate-800/30"
          role="region"
          aria-label="Bodenreinigung Service Bild"
        >
        <div className="max-w-6xl mx-auto">
          <div className="text-center">
            <div className="suz-service-image-container mx-auto">
              <img
                src={serviceData.image}
                alt={`${serviceData.title} - SUZ Reinigung Premium Service`}
                className="suz-service-image"
                loading="lazy"
                decoding="async"
                itemProp="image"
              />
            </div>
          </div>
        </div>
      </section>

        {/* Enhanced Features & Benefits Section */}
        <section
          className="suz-section-standard bg-slate-900/30"
          role="region"
          aria-label="Leistungen und Vorteile"
        >
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12">
              <div className="animate-fade-in">
                <header className="mb-8">
                  <h2
                    className="suz-text-display-md font-bold text-slate-100 mb-4"
                    data-ai-content="features-heading"
                  >
                    Unsere Leistungen
                  </h2>
                  <p className="suz-text-body-lg text-slate-400">
                    Professionelle Bodenreinigung für alle Bodenarten
                  </p>
                </header>
                <div className="grid gap-4" role="list">
                  {serviceData.features.map((feature, index) => (
                    <div
                      key={index}
                      className="suz-card-glass rounded-lg border border-white/10 p-4 group hover:border-purple-400/30 transition-all duration-300"
                      role="listitem"
                    >
                      <div className="flex items-center gap-3">
                        <Check className="w-5 h-5 text-purple-400 flex-shrink-0 transition-transform group-hover:scale-110" />
                        <span className="suz-text-body-md text-slate-300">{feature}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="animate-fade-in">
                <header className="mb-8">
                  <h2
                    className="suz-text-display-md font-bold text-slate-100 mb-4"
                    data-ai-content="benefits-heading"
                  >
                    Ihre Vorteile</h2>
              <div className="grid gap-4">
                {serviceData.benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <Star className="w-5 h-5 text-purple-600 flex-shrink-0" />
                    <span className="text-gray-300">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Process */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-slate-800/50">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-white mb-12">
            Unser Reinigungsprozess
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {serviceData.process.map((step, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 mb-4 bg-purple-100 rounded-full">
                  <span className="text-2xl font-bold text-purple-600">{step.step}</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">{step.title}</h3>
                <p className="text-gray-300">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-white mb-12">
            {serviceData.pricing.title}
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {serviceData.pricing.options.map((option, index) => (
              <div key={index} className="bg-slate-800/50 rounded-lg p-8 shadow-lg backdrop-blur-sm border border-white/10">
                <h3 className="text-xl font-semibold text-white mb-2">{option.name}</h3>
                <div className="text-3xl font-bold text-purple-600 mb-4">{option.price}</div>
                <p className="text-gray-300 mb-6">{option.description}</p>
                <ul className="space-y-2">
                  {option.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-purple-600" />
                      <span className="text-sm text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      <TrustIndicators layout="compact" />

      {/* FAQ */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-white mb-12">
            Häufig gestellte Fragen
          </h2>
          <div className="space-y-6">
            {serviceData.faqs.map((faq, index) => (
              <div key={index} className="bg-slate-800/50 rounded-lg p-6 shadow-sm border border-white/10 backdrop-blur-sm">
                <h3 className="text-lg font-semibold text-white mb-2">{faq.question}</h3>
                <p className="text-gray-300">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Quote Request */}
      <section id="quote" className="py-16 px-4 sm:px-6 lg:px-8 bg-purple-50">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">
              Kostenloses Angebot anfordern
            </h2>
            <p className="text-gray-300 max-w-2xl mx-auto">
              Erhalten Sie ein unverbindliches Angebot für Ihre Bodenreinigung. 
              Wir melden uns innerhalb von 24 Stunden bei Ihnen.
            </p>
          </div>
          <QuoteRequestForm />
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Bodenreinigung;